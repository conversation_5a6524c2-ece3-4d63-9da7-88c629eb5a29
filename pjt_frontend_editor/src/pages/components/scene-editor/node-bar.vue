<template>
  <div>
    <el-tree
        ref="treeRef"
        :data="gameNodes"
        :props="gameNodeProps"
        highlight-current
        :current-node-key="currentKey"
        node-key="name"
        :default-expanded-keys="expandedKeys"
        @node-click="handleGameNodeClick"
    >
      <template #default="{ node, data }">
        <span class="tree-node">
          <div :class="['node-icon', getNodeIconClass(node, data)]">
            <img :src="`../../assets/scene-editor/icon/${getNodeIconName(node, data)}.svg`" alt="camera" class="camera-svg" />
          </div>
          <span class="node-label">{{ node.label }}</span>
        </span>
      </template>
    </el-tree>
  </div>
</template>

<script>
import { Monitor, Document } from '@element-plus/icons-vue'

export default {
  name: 'node-bar',
  components: {
    Monitor,
    Document
  },
  data() {
    return {
      currentKey: null,
      expandedKeys: [],
      gameNodes: [
        {
          name: 'scene_0'
        }
      ],
      gameNodeProps: {
        children: 'children',
        label: 'name',
      }
    }
  },
  methods: {
    setData(data) {
      this.gameNodes = data;
      // 设置数据后，计算需要展开的节点
      this.$nextTick(() => {
        this.expandedKeys = this.getExpandedKeys(data, 2);
      });
    },
    // 递归获取前n层的所有节点key
    getExpandedKeys(nodes, maxLevel, currentLevel = 1) {
      let keys = [];
      if (!nodes || currentLevel > maxLevel) return keys;

      nodes.forEach(node => {
        if (currentLevel < maxLevel) {
          keys.push(node.name);
          if (node.children && node.children.length > 0) {
            keys = keys.concat(this.getExpandedKeys(node.children, maxLevel, currentLevel + 1));
          }
        }
      });

      return keys;
    },
    handleGameNodeClick(e) {
    },
    getNodeIconName(node, data) {
      if (data.type && data.type.length > 0) {
        return data.type;
      }
      return 'Node';
    },
    getNodeIcon(node, data) {
      console.log(node, data)
      // 第一层是场景图标
      if (node.level === 1) {
        return 'Monitor'; // 场景图标
      }
      // 其他层级是普通节点图标
      else {
        return 'Document'; // 普通节点图标
      }
    },
    isCameraNode(data) {
      return data && data.type === 'Camera';
    },
    getNodeIconClass(node, data) {
      // 根据层级和类型返回不同的CSS类名
      if (node.level === 1) {
        return 'scene-icon'; // 场景图标样式
      } else if (data && data.type === 'Camera') {
        return 'camera-icon'; // 相机图标样式
      } else {
        return 'node-icon-normal'; // 普通节点图标样式
      }
    }
  },
  mounted() {
  }
}
</script>

<style scoped lang="less">
.tree-node {
  display: flex;
  align-items: center;

  .node-icon {
    margin-right: 6px;
    font-size: 14px;
    display: flex;
    align-items: center;

    // 场景图标样式（蓝色）
    &.scene-icon {
      color: #409EFF;
    }

    // 相机图标样式（绿色）
    &.camera-icon {
      color: #67C23A;
    }

    // 普通节点图标样式（灰色）
    &.node-icon-normal {
      color: #909399;
    }

    .camera-svg {
      width: 14px;
      height: 14px;
      fill: currentColor;
    }
  }

  .node-label {
    font-size: 14px;
  }
}
</style>